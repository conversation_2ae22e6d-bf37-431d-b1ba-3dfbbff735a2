#!/bin/bash
#
# mount-archive-disks.sh - Mount archive disks based on serial number mapping
#
# Description:
#   Scans all /dev/sd* devices, retrieves their serial numbers using hdparm,
#   and mounts them to /mnt/*simulatornumber* directories based on a hardcoded
#   serial number to simulator mapping (no database interaction).
#
# Usage:
#   ./mount-archive-disks.sh [options]
#
# Options:
#   -u, --unmount    Unmount all previously mounted archive disks
#   -v, --verbose    Enable verbose output
#   -h, --help       Show this help message
#

# Configuration
MOUNT_BASE="/mnt"

# Device serial numbers and their corresponding simulator names
# Based on the mapping from ocs-flyright script
declare -A device_serials=(
    ["657"]="Z4Z83RHP"
    ["1018"]="9WM89ES0"
    ["1678_1697"]="Z1E1T00R"
    ["1477"]="Z4Z9FNMZ"
    ["1329"]="WD-WCC4E3TA1SK8"
    ["393_423"]="Z4E0B6E2"
    ["internal"]="WD-WCC6Y4EEKNNV"
)

# Default options
VERBOSE=false
UNMOUNT_ONLY=false

# Color codes for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    local color=$1
    local message=$2
    if [ "$VERBOSE" = true ]; then
        echo -e "${color}${message}${NC}"
    fi
}

print_info() {
    echo -e "${BLUE}INFO:${NC} $1"
}

print_success() {
    echo -e "${GREEN}SUCCESS:${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}WARNING:${NC} $1"
}

print_error() {
    echo -e "${RED}ERROR:${NC} $1"
}

# Function to show help
show_help() {
    cat << EOF
mount-archive-disks.sh - Mount archive disks based on serial number mapping

USAGE:
    $0 [OPTIONS]

DESCRIPTION:
    Scans all /dev/sd* devices, retrieves their serial numbers using hdparm,
    and mounts them to /mnt/*simulatornumber* directories based on a hardcoded
    serial number to simulator mapping.

OPTIONS:
    -u, --unmount    Unmount all previously mounted archive disks and exit
    -v, --verbose    Enable verbose output
    -h, --help       Show this help message

EXAMPLES:
    $0                    # Mount all detected archive disks
    $0 -v                 # Mount with verbose output
    $0 -u                 # Unmount all mounted archive disks
    $0 --unmount --verbose # Unmount with verbose output

MOUNT POINTS:
    Archive disks are mounted to /mnt/SIMULATOR_NAME/ based on the following mapping:
    
    Serial Number        -> Simulator -> Mount Point
    Z4Z83RHP            -> c208       -> /mnt/657
    9WM89ES0            -> efis       -> /mnt/1018
    Z1E1T00R            -> ka_g1000   -> /mnt/1678_1697
    Z4Z9FNMZ            -> c90        -> /mnt/1477
    WD-WCC4E3TA1SK8     -> pl21       -> /mnt/1329
    Z4E0B6E2            -> dash       -> /mnt/393_423
    WD-WCC6Y4EEKNNV     -> internal   -> /mnt/internal

EOF
}

# Function to parse command line arguments
parse_arguments() {
    while [[ $# -gt 0 ]]; do
        case $1 in
            -u|--unmount)
                UNMOUNT_ONLY=true
                shift
                ;;
            -v|--verbose)
                VERBOSE=true
                shift
                ;;
            -h|--help)
                show_help
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                show_help
                exit 1
                ;;
        esac
    done
}

# Function to create mount point directory
create_mount_point() {
    local mount_point="$1"
    
    if [ ! -d "$mount_point" ]; then
        print_status "$YELLOW" "Creating mount point: $mount_point"
        if mkdir -p "$mount_point"; then
            print_status "$GREEN" "Mount point created successfully"
        else
            print_error "Failed to create mount point: $mount_point"
            return 1
        fi
    else
        print_status "$BLUE" "Mount point already exists: $mount_point"
    fi
    return 0
}

# Function to unmount a device if it's mounted
unmount_device() {
    local device="$1"
    local mount_point="$2"
    
    # Check if device is mounted
    if mountpoint -q "$mount_point" 2>/dev/null; then
        print_info "Unmounting $device from $mount_point"
        if umount "$mount_point" 2>/dev/null; then
            print_success "Successfully unmounted $device"
        else
            print_warning "Failed to unmount $device from $mount_point"
            return 1
        fi
    fi
    return 0
}

# Function to mount a device
mount_device() {
    local device="$1"
    local mount_point="$2"
    local simulator="$3"
    
    # Check if already mounted elsewhere
    local current_mount=$(findmnt -S "$device" -o TARGET -n 2>/dev/null)
    if [ -n "$current_mount" ]; then
        if [ "$current_mount" = "$mount_point" ]; then
            print_info "Device $device already mounted at correct location: $mount_point"
            return 0
        else
            print_warning "Device $device is mounted at $current_mount, unmounting..."
            umount "$device" 2>/dev/null
        fi
    fi
    
    # Create mount point if it doesn't exist
    if ! create_mount_point "$mount_point"; then
        return 1
    fi
    
    # Attempt to mount the device
    print_info "Mounting $device ($simulator archive) to $mount_point"
    if mount "$device" "$mount_point" 2>/dev/null; then
        print_success "Successfully mounted $device to $mount_point"
        return 0
    else
        print_error "Failed to mount $device to $mount_point"
        return 1
    fi
}

# Function to unmount all archive disks in /mnt/*
unmount_all_drives() {
    print_info "Unmounting all archive disks in $MOUNT_BASE/*"
    
    local unmounted_count=0
    local failed_count=0
    
    # Check each known simulator mount point
    for simulator in "${!device_serials[@]}"; do
        local mount_point="$MOUNT_BASE/$simulator"
        
        if [ -d "$mount_point" ] && mountpoint -q "$mount_point" 2>/dev/null; then
            local device=$(findmnt -M "$mount_point" -o SOURCE -n 2>/dev/null)
            print_status "$YELLOW" "Found mounted archive disk: $device at $mount_point"
            
            if umount "$mount_point" 2>/dev/null; then
                print_success "Unmounted $device from $mount_point"
                ((unmounted_count++))
            else
                print_error "Failed to unmount $device from $mount_point"
                ((failed_count++))
            fi
        fi
    done
    
    print_info "Unmount summary: $unmounted_count successful, $failed_count failed"
    return $failed_count
}

# Function to scan and mount archive disks
scan_and_mount_drives() {
    print_info "Scanning /dev/sd* devices for archive disks..."
    
    # Check if any /dev/sd* devices exist
    if ! ls /dev/sd* >/dev/null 2>&1; then
        print_error "No SCSI/SATA devices found"
        return 1
    fi
    
    local mounted_count=0
    local failed_count=0
    local unknown_count=0
    
    for device in /dev/sd*; do
        # Skip if not a block device or if it's a partition
        if [ ! -b "$device" ] || [[ "$device" =~ [0-9]$ ]]; then
            continue
        fi
        
        print_status "$BLUE" "Processing device: $device"
        
        # Get the serial number using hdparm
        local serial_number=$(sudo hdparm -I "$device" 2>/dev/null | grep "Serial Number" | awk '{print $3}')
        
        if [ -z "$serial_number" ]; then
            print_warning "Could not retrieve serial number for $device"
            ((failed_count++))
            continue
        fi
        
        print_status "$GREEN" "Device $device has serial: $serial_number"
        
        # Check if this serial matches any known archive disk
        local found=false
        local simulator=""
        
        for sim_name in "${!device_serials[@]}"; do
            if [ "$serial_number" == "${device_serials[$sim_name]}" ]; then
                simulator="$sim_name"
                found=true
                break
            fi
        done
        
        if [ "$found" = false ]; then
            print_status "$YELLOW" "Serial number $serial_number not recognized as archive disk for device $device"
            ((unknown_count++))
            continue
        fi
        
        print_status "$GREEN" "Recognized as $simulator archive disk"
        
        # Create mount point path based on simulator
        local mount_point="$MOUNT_BASE/$simulator"
        
        # Mount the device
        if mount_device "$device" "$mount_point" "$simulator"; then
            ((mounted_count++))
        else
            ((failed_count++))
        fi
    done
    
    print_info "Mount summary: $mounted_count successful, $failed_count failed, $unknown_count unknown"
    return $failed_count
}

# Main function
main() {
    parse_arguments "$@"
    
    print_info "Starting archive disk mounting script..."
    
    # Check if running as root or with sudo
    if [ "$EUID" -ne 0 ]; then
        print_error "This script requires root privileges. Please run with sudo."
        exit 1
    fi
    
    if [ "$UNMOUNT_ONLY" = true ]; then
        unmount_all_drives
        exit $?
    fi
    
    # Scan and mount archive disks
    scan_and_mount_drives
    exit $?
}

# Run main function with all arguments
main "$@"
